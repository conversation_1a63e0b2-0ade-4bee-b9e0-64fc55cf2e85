# 爬虫环境配置示例

# 基础配置
DEBUG=true
LOG_LEVEL=INFO

# 数据库配置
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require

# AWS Bedrock Claude配置
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=wDQ2PdBijgoO5zW6i5QG3GPGXenx+vR/edgRPEs8
AWS_REGION=us-west-2
BEDROCK_MODEL_ID=anthropic.claude-3-haiku-20240307-v1:0
BEDROCK_MAX_TOKENS=8000
BEDROCK_TEMPERATURE=0.7

# 爬虫配置
REQUEST_DELAY=1.0
REQUEST_TIMEOUT=30
MAX_RETRIES=3
CONCURRENT_REQUESTS=5

# 代理配置（可选）
USE_PROXY=false
PROXY_LIST=

# 输出配置
OUTPUT_DIR=data
IMAGES_DIR=images

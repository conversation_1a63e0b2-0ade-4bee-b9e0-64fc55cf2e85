<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="2d2e013c-2772-4a2f-8762-ae11a4f234bd" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/../../github/AiChatFun/chatgpt.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../github/AiChatFun/config.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../github/AiChatFun/interface.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../github/AiChatFun/paper.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/../.." />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="$PROJECT_DIR$/../../workdata/scala" />
        <option name="userSettingsFile" value="$PROJECT_DIR$/../../软件/apache-maven-3.8.8/conf/settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectCodeStyleSettingsMigration">
    <option name="version" value="2" />
  </component>
  <component name="ProjectId" id="30GT8NrIMEKW7fgRLwnb7iiziB7" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "WebServerToolWindowFactoryState": "false",
    "last_opened_file_path": "/Users/<USER>/Downloads/startups/LookAiTools",
    "settings.editor.selected.configurable": "MavenSettings",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="2d2e013c-2772-4a2f-8762-ae11a4f234bd" name="Changes" comment="" />
      <created>1753252750600</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753252750600</updated>
      <workItem from="1753252752096" duration="5000" />
    </task>
    <servers />
  </component>
</project>